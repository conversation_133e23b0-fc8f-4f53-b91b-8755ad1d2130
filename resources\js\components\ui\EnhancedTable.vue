<template>
  <div class="enhanced-table-container">
    <!-- Table Header with Actions -->
    <div v-if="showHeader" class="bg-white px-6 py-4 border-b border-gray-200 rounded-t-lg">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <!-- Title and Description -->
        <div>
          <h3 v-if="title" class="text-lg font-semibold text-gray-900">{{ title }}</h3>
          <p v-if="subtitle" class="text-sm text-gray-600 mt-1">{{ subtitle }}</p>
        </div>

        <!-- Actions -->
        <div class="flex flex-col sm:flex-row gap-3">
          <!-- Search -->
          <div v-if="searchable" class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
            </div>
            <input
              v-model="searchQuery"
              type="text"
              :placeholder="searchPlaceholder"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-primary-500 focus:border-primary-500 sm:text-sm"
            />
          </div>

          <!-- Filter Toggle -->
          <Button
            v-if="filterable && filters.length > 0"
            variant="outline"
            size="sm"
            @click="showFilters = !showFilters"
          >
            <FunnelIcon class="h-4 w-4 mr-2" />
            Filters
            <ChevronDownIcon 
              :class="['h-4 w-4 ml-2 transition-transform', showFilters ? 'rotate-180' : '']" 
            />
          </Button>

          <!-- Export -->
          <Button
            v-if="exportable"
            variant="outline"
            size="sm"
            @click="handleExport"
          >
            <ArrowDownTrayIcon class="h-4 w-4 mr-2" />
            Export
          </Button>

          <!-- Create Button -->
          <Button
            v-if="createButton"
            variant="primary"
            size="sm"
            @click="$emit('create')"
          >
            <PlusIcon class="h-4 w-4 mr-2" />
            {{ createButtonText }}
          </Button>
        </div>
      </div>

      <!-- Filters Panel -->
      <div v-if="showFilters && filters.length > 0" class="mt-4 pt-4 border-t border-gray-200">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <div v-for="filter in filters" :key="filter.key" class="space-y-1">
            <label class="block text-sm font-medium text-gray-700">{{ filter.label }}</label>
            
            <!-- Text Filter -->
            <input
              v-if="filter.type === 'text'"
              v-model="filterValues[filter.key]"
              type="text"
              :placeholder="filter.placeholder"
              class="form-input"
            />
            
            <!-- Select Filter -->
            <Select
              v-else-if="filter.type === 'select'"
              v-model="filterValues[filter.key]"
              :options="filter.options"
              :placeholder="filter.placeholder"
            />
            
            <!-- Date Range Filter -->
            <div v-else-if="filter.type === 'daterange'" class="flex gap-2">
              <input
                v-model="filterValues[filter.key + '_from']"
                type="date"
                class="form-input flex-1"
              />
              <input
                v-model="filterValues[filter.key + '_to']"
                type="date"
                class="form-input flex-1"
              />
            </div>
          </div>
        </div>
        
        <div class="flex justify-end mt-4 gap-2">
          <Button variant="outline" size="sm" @click="clearFilters">
            Clear Filters
          </Button>
          <Button variant="primary" size="sm" @click="applyFilters">
            Apply Filters
          </Button>
        </div>
      </div>
    </div>

    <!-- Bulk Actions Bar -->
    <div 
      v-if="selectable && selectedRows.length > 0 && bulkActions.length > 0"
      class="bg-primary-50 border-b border-primary-200 px-6 py-3"
    >
      <div class="flex items-center justify-between">
        <div class="flex items-center">
          <span class="text-sm font-medium text-primary-700">
            {{ selectedRows.length }} item{{ selectedRows.length === 1 ? '' : 's' }} selected
          </span>
        </div>
        <div class="flex items-center gap-2">
          <Button
            v-for="action in bulkActions"
            :key="action.key"
            :variant="action.variant || 'outline'"
            size="sm"
            @click="$emit('bulk-action', action.key, selectedRows)"
          >
            <component :is="action.icon" v-if="action.icon" class="h-4 w-4 mr-2" />
            {{ action.label }}
          </Button>
          <Button variant="ghost" size="sm" @click="clearSelection">
            <XMarkIcon class="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>

    <!-- Enhanced Table -->
    <div class="bg-white shadow-sm rounded-b-lg overflow-hidden">
      <VueGoodTable
        :columns="enhancedColumns"
        :rows="filteredData"
        :pagination-options="paginationOptions"
        :search-options="searchOptions"
        :sort-options="sortOptions"
        :loading="loading"
        :styleClass="tableStyleClass"
        @on-page-change="$emit('page-change', $event)"
        @on-sort-change="$emit('sort-change', $event)"
        @on-per-page-change="$emit('per-page-change', $event)"
        @on-row-click="handleRowClick"
      >
        <!-- Custom column templates -->
        <template #table-row="props">
          <span v-if="props.column.field === '__selection__'">
            <input
              type="checkbox"
              :checked="isRowSelected(props.row)"
              @change="toggleRowSelection(props.row)"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
          </span>
          <span v-else-if="props.column.field === '__actions__'">
            <div class="flex items-center gap-1">
              <Button
                v-for="action in rowActions"
                :key="action.key"
                :variant="action.variant || 'ghost'"
                size="sm"
                @click.stop="$emit('row-action', action.key, props.row, props.index)"
              >
                <component :is="action.icon" v-if="action.icon" class="h-4 w-4" />
                <span v-if="action.label" class="ml-1">{{ action.label }}</span>
              </Button>
            </div>
          </span>
          <slot
            v-else
            :name="`column-${props.column.field}`"
            :row="props.row"
            :column="props.column"
            :index="props.index"
            :value="props.formattedRow[props.column.field]"
          >
            <span>{{ props.formattedRow[props.column.field] }}</span>
          </slot>
        </template>

        <!-- Empty state slot -->
        <template #emptystate>
          <div class="text-center py-12">
            <div class="text-6xl mb-4 opacity-20">📋</div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">{{ emptyStateTitle }}</h3>
            <p class="text-gray-500 mb-6">{{ emptyStateMessage }}</p>
            <Button v-if="createButton" variant="primary" @click="$emit('create')">
              <PlusIcon class="h-4 w-4 mr-2" />
              {{ createButtonText }}
            </Button>
          </div>
        </template>
      </VueGoodTable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue';
import { VueGoodTable, type VgtColumn } from 'vue-good-table-next';
import 'vue-good-table-next/dist/vue-good-table-next.css';
import { Button, Select } from '@/components/ui';
import {
  MagnifyingGlassIcon,
  FunnelIcon,
  ChevronDownIcon,
  ArrowDownTrayIcon,
  PlusIcon,
  XMarkIcon,
} from '@heroicons/vue/24/outline';

// Types
interface Column extends VgtColumn {
  key?: string;
  render?: (value: any, row: any, index: number) => string | any;
}

interface Filter {
  key: string;
  label: string;
  type: 'text' | 'select' | 'daterange';
  placeholder?: string;
  options?: Array<{ label: string; value: any }>;
}

interface Action {
  key: string;
  label?: string;
  icon?: any;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'danger';
}

interface Props {
  // Data
  data: any[];
  columns: Column[];
  loading?: boolean;
  
  // Header
  title?: string;
  subtitle?: string;
  showHeader?: boolean;
  
  // Features
  searchable?: boolean;
  searchPlaceholder?: string;
  filterable?: boolean;
  filters?: Filter[];
  exportable?: boolean;
  selectable?: boolean;
  
  // Actions
  createButton?: boolean;
  createButtonText?: string;
  rowActions?: Action[];
  bulkActions?: Action[];
  
  // Table options
  striped?: boolean;
  bordered?: boolean;
  hover?: boolean;
  
  // Empty state
  emptyStateTitle?: string;
  emptyStateMessage?: string;
  
  // Pagination
  paginationEnabled?: boolean;
  perPage?: number;
  perPageDropdown?: number[];
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showHeader: true,
  searchable: true,
  searchPlaceholder: 'Search...',
  filterable: false,
  filters: () => [],
  exportable: false,
  selectable: false,
  createButton: false,
  createButtonText: 'Create New',
  rowActions: () => [],
  bulkActions: () => [],
  striped: true,
  bordered: false,
  hover: true,
  emptyStateTitle: 'No data found',
  emptyStateMessage: 'Get started by creating your first item.',
  paginationEnabled: true,
  perPage: 10,
  perPageDropdown: () => [5, 10, 25, 50, 100],
});

const emit = defineEmits<{
  'create': [];
  'row-action': [action: string, row: any, index: number];
  'bulk-action': [action: string, rows: any[]];
  'row-click': [row: any, index: number];
  'page-change': [params: any];
  'sort-change': [params: any];
  'per-page-change': [params: any];
  'filter-change': [filters: Record<string, any>];
  'export': [data: any[]];
}>();

// Reactive state
const searchQuery = ref('');
const showFilters = ref(false);
const filterValues = ref<Record<string, any>>({});
const selectedRows = ref<any[]>([]);

// Computed properties
const enhancedColumns = computed(() => {
  const cols: VgtColumn[] = [];

  // Add selection column if selectable
  if (props.selectable) {
    cols.push({
      label: '',
      field: '__selection__',
      sortable: false,
      width: '50px',
      thClass: 'text-center',
      tdClass: 'text-center',
    });
  }

  // Add main columns
  cols.push(...props.columns.map(col => ({
    ...col,
    field: col.field || col.key || '',
  })));

  // Add actions column if row actions exist
  if (props.rowActions.length > 0) {
    cols.push({
      label: 'Actions',
      field: '__actions__',
      sortable: false,
      width: `${Math.max(props.rowActions.length * 40, 100)}px`,
      thClass: 'text-center',
      tdClass: 'text-center',
    });
  }

  return cols;
});

const filteredData = computed(() => {
  let data = [...props.data];

  // Apply search filter
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    data = data.filter(row => {
      return props.columns.some(col => {
        const field = col.field || col.key || '';
        const value = getNestedValue(row, field);
        return String(value).toLowerCase().includes(query);
      });
    });
  }

  // Apply custom filters
  Object.entries(filterValues.value).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      if (key.endsWith('_from') || key.endsWith('_to')) {
        // Handle date range filters
        const baseKey = key.replace(/_from$|_to$/, '');
        const fromValue = filterValues.value[baseKey + '_from'];
        const toValue = filterValues.value[baseKey + '_to'];

        if (fromValue || toValue) {
          data = data.filter(row => {
            const rowValue = new Date(getNestedValue(row, baseKey));
            const from = fromValue ? new Date(fromValue) : null;
            const to = toValue ? new Date(toValue) : null;

            if (from && to) {
              return rowValue >= from && rowValue <= to;
            } else if (from) {
              return rowValue >= from;
            } else if (to) {
              return rowValue <= to;
            }
            return true;
          });
        }
      } else if (!key.endsWith('_from') && !key.endsWith('_to')) {
        // Handle other filters
        data = data.filter(row => {
          const rowValue = getNestedValue(row, key);
          if (Array.isArray(value)) {
            return value.includes(rowValue);
          }
          return String(rowValue).toLowerCase().includes(String(value).toLowerCase());
        });
      }
    }
  });

  return data;
});

const tableStyleClass = computed(() => {
  const classes = ['vgt-table'];
  if (props.striped) classes.push('striped');
  if (props.bordered) classes.push('bordered');
  return classes.join(' ');
});

const paginationOptions = computed(() => ({
  enabled: props.paginationEnabled,
  mode: 'records' as const,
  perPage: props.perPage,
  perPageDropdown: props.perPageDropdown,
  dropdownAllowAll: false,
  nextLabel: 'Next',
  prevLabel: 'Previous',
  rowsPerPageLabel: 'Rows per page',
  ofLabel: 'of',
  pageLabel: 'page',
}));

const searchOptions = computed(() => ({
  enabled: false, // We handle search manually
}));

const sortOptions = computed(() => ({
  enabled: true,
  multipleColumns: false,
}));

// Methods
const getNestedValue = (obj: any, path: string): any => {
  return path.split('.').reduce((current, key) => current?.[key], obj);
};

const isRowSelected = (row: any): boolean => {
  return selectedRows.value.some(selected =>
    getRowKey(selected) === getRowKey(row)
  );
};

const getRowKey = (row: any): string | number => {
  return row.id || row.key || JSON.stringify(row);
};

const toggleRowSelection = (row: any) => {
  const isSelected = isRowSelected(row);

  if (isSelected) {
    selectedRows.value = selectedRows.value.filter(selected =>
      getRowKey(selected) !== getRowKey(row)
    );
  } else {
    selectedRows.value.push(row);
  }
};

const clearSelection = () => {
  selectedRows.value = [];
};

const handleRowClick = (params: any) => {
  if (props.selectable) {
    toggleRowSelection(params.row);
  }
  emit('row-click', params.row, params.pageIndex);
};

const clearFilters = () => {
  filterValues.value = {};
  emit('filter-change', {});
};

const applyFilters = () => {
  emit('filter-change', { ...filterValues.value });
};

const handleExport = () => {
  emit('export', filteredData.value);
};

// Watch for filter changes
watch(filterValues, (newFilters) => {
  emit('filter-change', { ...newFilters });
}, { deep: true });
</script>

<style scoped>
.enhanced-table-container {
  @apply bg-white rounded-lg shadow-sm overflow-hidden;
}

/* Custom enhancements for better UX */
.enhanced-table-container .vgt-table {
  border-radius: 0;
  box-shadow: none;
  border: none;
}

.enhanced-table-container .vgt-table thead th:first-child {
  border-top-left-radius: 0;
}

.enhanced-table-container .vgt-table thead th:last-child {
  border-top-right-radius: 0;
}

/* Smooth transitions for interactive elements */
.enhanced-table-container input[type="checkbox"] {
  transition: all 0.15s ease;
}

.enhanced-table-container input[type="checkbox"]:hover {
  transform: scale(1.05);
}

/* Enhanced focus states */
.enhanced-table-container input:focus,
.enhanced-table-container select:focus {
  outline: none;
  ring: 2px solid rgba(0, 104, 255, 0.2);
  border-color: #0068ff;
}

/* Loading state improvements */
.enhanced-table-container .vgt-loading {
  backdrop-filter: blur(2px);
  background-color: rgba(255, 255, 255, 0.9) !important;
}

/* Mobile responsiveness */
@media (max-width: 640px) {
  .enhanced-table-container .vgt-table {
    font-size: 0.875rem;
  }

  .enhanced-table-container .vgt-table thead th,
  .enhanced-table-container .vgt-table tbody td {
    padding: 0.5rem 0.75rem;
  }
}
</style>
